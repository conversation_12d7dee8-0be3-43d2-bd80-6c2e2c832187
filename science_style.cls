% --- START OF FILE science_style.cls ---
%
% A LaTeX class for Science journal articles.
%
% This class is designed to strictly follow the formatting requirements
% outlined in the official 'science_template.tex'. It is adapted from
% an earlier MDPI-style class but has been significantly modified.
%
% Key features matching 'science_template.tex':
% - Times font (newtx), 12pt, 1.5 line spacing.
% - 1-inch margins on US Letter paper.
% - Simplified title/author block without 'authblk'.
% - Abstract as a 'quotation' environment, separate from the title block.
% - Bold figure/table labels (e.g., "Figure 1").
% - Unnumbered section headings.
% - 'scicite' package and 'sciencemag' bibliography style.
%
% v1.0: 2024/05/22, Initial creation based on analysis of science_template.tex
%
\NeedsTeXFormat{LaTeX2e}
\ProvidesClass{science_style}[2024/05/22, v1.0 Science Journal Style]

\RequirePackage[utf8]{inputenc}
\RequirePackage[english]{babel}
\RequirePackage{ifthen}
\ProcessOptions\relax
\LoadClass[12pt]{article} % Science uses 12pt text

% --- 1. FONT & SPACING (As per science_template.tex) ---
\RequirePackage{newtxtext,newtxmath} % Science uses Times font
\linespread{1.5} % Double line spacing
\frenchspacing % One space after each sentence

% --- 2. PAGE LAYOUT (As per science_template.tex) ---
\RequirePackage[letterpaper,margin=1in]{geometry}

% --- 3. CORE PACKAGES ---
\RequirePackage{graphicx}
\RequirePackage{amsmath}
\RequirePackage{url} % For handling URLs in references

% --- 4. TITLE & AUTHOR BLOCK (MODIFIED to match science_template.tex) ---
% REMOVED: \usepackage{authblk}. Science template uses standard \author command.
% We redefine \maketitle to match the simple, left-aligned style.
\renewcommand{\@maketitle}{%
    \null
    \thispagestyle{empty}% The first page has no header/footer
    \begin{center}%
        {\LARGE \bfseries \boldmath \@title \par}% Title style from template
    \end{center}%
    \par
    \vspace{1.5em}
    % Authors are handled by the standard \author command, centered.
    \begin{center}
        {\large \<AUTHOR>
    \end{center}
    \par
    % \@date is intentionally ignored as per \date{} in the template
    \vspace{2em}
}

% --- 5. ABSTRACT (MODIFIED to match science_template.tex) ---
% The abstract is now a simple 'quotation' environment, with no "Abstract" heading.
% It is called *after* \maketitle in the main .tex file.
% The old, complex abstract definition that integrated it into \maketitle is removed.
\renewenvironment{abstract}{\quotation}{\endquotation}

% --- 6. SECTION HEADINGS (MODIFIED for Science style) ---
% Science uses unnumbered headings. We use titlesec to make \section*, \subsection*
% the default behavior of \section, \subsection.
\RequirePackage[explicit]{titlesec}
\setcounter{secnumdepth}{0} % No section numbering

% \section is typically used for backmatter (e.g., Acknowledgments)
\titleformat{\section}
  {\normalfont\Large\bfseries}
  {}
  {0em}
  {#1}
\titlespacing*{\section}{0pt}{3.5ex plus 1ex minus .2ex}{2.3ex plus .2ex}

% \subsection is used for main text headings in some article types
\titleformat{\subsection}
  {\normalfont\large\bfseries}
  {}
  {0em}
  {#1}
\titlespacing*{\subsection}{0pt}{3.25ex plus 1ex minus .2ex}{1.5ex plus .2ex}

% \subsubsection is not common, but defined for consistency
\titleformat{\subsubsection}
  {\normalfont\normalsize\bfseries}
  {}
  {0em}
  {#1}
\titlespacing*{\subsubsection}{0pt}{3.25ex plus 1ex minus .2ex}{1.5ex plus .2ex}

% \paragraph is used for run-in headings in Acknowledgments
\titleformat{\paragraph}[runin]
  {\normalfont\normalsize\bfseries}
  {}
  {0em}
  {#1}
  [.] % Add a period after the paragraph title

% --- 7. FIGURE & TABLE CAPTIONS (As per science_template.tex) ---
\RequirePackage{caption}

% Figure and Table labels are bold (e.g., "Figure 1")
\makeatletter
\renewcommand{\fnum@figure}{\textbf{Figure \thefigure}}
\renewcommand{\fnum@table}{\textbf{Table \thetable}}
\makeatother

% Note: The first sentence of the caption being bold is done MANUALLY
% in the .tex file, as shown in science_template.tex, e.g., \caption{\textbf{Title.} Text...}
\captionsetup{
    labelsep=period,
    justification=justified,
    singlelinecheck=false,
    font=normalsize % Captions are in the main text font size
}
\captionsetup[table]{position=top} % Table captions go above the table
\captionsetup[figure]{position=bottom} % Figure captions go below the figure

% Hyperref should be loaded last
\RequirePackage[colorlinks=true, allcolors=blue, pdfstartview=FitH, bookmarks=true, bookmarksnumbered=false]{hyperref}

% --- 8. REFERENCES (As per science_template.tex) ---
\RequirePackage{scicite} % Formats citation numbers in Science style
\renewcommand\refname{References and Notes} % Section heading for references

% Set default bibliography style. User can override in their .tex file.
\AtBeginDocument{\bibliographystyle{sciencemag}}

% --- 9. OTHER SETTINGS ---
% Basic page style
\RequirePackage{fancyhdr}
\pagestyle{fancy}
\fancyhf{}
\cfoot{\thepage}
\renewcommand{\headrulewidth}{0pt}
\renewcommand{\footrulewidth}{0pt}


\endinput
% --- END OF FILE science_style.cls ---