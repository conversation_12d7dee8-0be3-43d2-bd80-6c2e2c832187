% cSpell:disable
\documentclass[fleqn,10pt]{wlscirep_mdpi_style} 
%%% <<< FIX END >>> %%%

\usepackage{palatino} % 添加Palatino字体支持
\usepackage[utf8]{inputenc}
\usepackage[T1]{fontenc}
\usepackage{graphicx} % For figures
\usepackage{amsmath}  % For math symbols

\usepackage{multirow}
\usepackage{array}
\usepackage{tabularx}
\usepackage{booktabs} % For better tables
\usepackage{makecell}  % 核心！用于单元格增强功能
\usepackage{multirow} % For multirow cells in tables
\usepackage{url}      % For URLs
% FIX 3: Add xurl package for better URL breaking in bibliography
\usepackage{xurl}     % Allows URLs to break almost anywhere
% Add line numbers package
\usepackage{lineno}   % For line numbers
% Configure line numbers to restart on each page
\renewcommand\linenumberfont{\normalfont\tiny\sffamily}
\setlength\linenumbersep{3mm}
\modulolinenumbers[1]  % Show every line number
\pagewiselinenumbers   % Restart numbering on each page
% FIX: BOOKMARKS - REMOVED hyperref load here. 
% The class file (wlscirep.cls) loads hyperref LATE with [bookmarks=true] option, 
% loading it here can cause conflicts. Let the class handle it.
%\usepackage{hyperref} % For links 
%%% <<< FIX START: Add placeins package for \FloatBarrier >>> %%%
\usepackage{placeins} 
%%% <<< FIX END >>> %%%
\usepackage{lipsum} % for dummy text

%%% <<< GEMINI ADDED: Move all floats (figures, tables) to the end of the document >>> %%%
\usepackage[nolists,tablesfirst,nomarkers]{endfloat}
%%% <<< END GEMINI ADDED >>> %%%

% --- 放宽浮动体放置的规则 (放在 \documentclass 和 \begin{document} 之间) ---
% NOTE: These parameters are already quite relaxed, which is good for allowing \FloatBarrier to work
% 增加浮动体在页面顶部所占的最大比例 (默认 0.7)
\renewcommand{\topfraction}{0.9} % increased slightly
% 增加浮动体在页面底部所占的最大比例 (默认 0.3)
\renewcommand{\bottomfraction}{0.8}
% 减少页面上文本区域所必须占有的最小比例 (默认 0.2)
\renewcommand{\textfraction}{0.1} % decreased slightly
% 增加一个"浮动页"所必须被浮动体占据的最小比例 (默认 0.5)
\renewcommand{\floatpagefraction}{0.8} % increased slightly
% 允许页面顶部放置的浮动体数量 (默认 2)
\setcounter{topnumber}{4}
% 允许页面底部放置的浮动体数量 (默认 1)
\setcounter{bottomnumber}{3}
% 允许整个页面放置的浮动体数量 (默认 3)
\setcounter{totalnumber}{6}
% --- 结束 ---

\renewcommand{\tabularxcolumn}[1]{m{#1}}
% Create a mapping for references - internal process, applied below
% (Reference list omitted for brevity) ...


\title{Resolving the signal ambiguity problem in satellite earthquake monitoring through environment-specific AI}

\author[1]{Pan Xiong}
\author[2]{Cheng Long}
\author[3]{Huiyu Zhou}
\author[4,5]{Roberto Battiston}
\author[6]{Angelo De Santis}
\author[7,*]{Xuhui Shen}

\affil[1]{Institute of Earthquake Forecasting, China Earthquake Administration, Beijing, China}
\affil[2]{College of Computing and Data Science, Nanyang Technological University, Singapore, Singapore}
\affil[3]{School of Computing and Mathematical Sciences, University of Leicester, Leicester, United Kingdom}
\affil[4]{Department of Physics, University of Trento, Trento, Italy}
\affil[5]{National Institute for Nuclear Physics, The Trento Institute for Fundamental Physics and Applications, Trento, Italy}
\affil[6]{Istituto Nazionale di Geofisica e Vulcanologia, Rome, Italy}
\affil[7]{National Space Science Center, Chinese Academy of Sciences, Beijing, China}


\affil[*]{Corresponding author: Xuhui Shen} % Adjust email if needed


%\keywords{Keyword1, Keyword2, Keyword3}

\begin{abstract}
For decades, satellite-based earthquake monitoring has been paralyzed by a fundamental paradox: the signal ambiguity problem. Microwave anomalies routinely detected before some earthquakes also occur without seismic consequence, while devastating earthquakes often strike without detectable precursors—rendering satellite monitoring largely ineffective for operational use. Here we demonstrate that this long-standing barrier is not an inherent physical limitation but a methodological blind spot. By analyzing 346.56 million microwave brightness temperature measurements across 154 major earthquakes (M$\ge$7.0) from 2013-2023, we reveal that reliable precursor signatures are environment-specific, not universal. Our knowledge-guided deep learning framework first classifies Earth's surface into five distinct zones, then discovers that marine environments achieve perfect detection through specific 89 GHz H-polarization patterns, while arid regions require entirely different frequency combinations. This environment-specific approach, implemented through our Weight-Enhanced Feature-Tailored Transformer (WE-FTT), achieves unprecedented discrimination between genuine precursors and environmental noise (MCC $\sim$0.84), a significant leap over conventional approaches. By resolving the signal ambiguity problem, this work transforms satellite earthquake monitoring from an inconsistent research tool into a potentially operational system for disaster preparedness.
\end{abstract}
\begin{document}

% Start line numbering
\linenumbers

\flushbottom
\maketitle
\thispagestyle{empty}

% Optional: Add a table of contents to check numbering
% \tableofcontents 
% \FloatBarrier
% \clearpage


%\noindent Please note: Abbreviations should be introduced at the first mention in the main text -- no abbreviations lists. Suggested structure of main text (not enforced) is provided below.

% FIX BOOKMARK: Changed \section* to \section
% FIX 1: No numbers will be displayed due to class file modification
\section{Introduction} \label{sec:intro}
Earthquakes remain among the most devastating natural hazards, and reliable short-term prediction is a grand challenge in Earth science \cite{akhoondzadehMultiPrecursorsAnalysis2018}. While satellite-based microwave remote sensing offers unique potential for precursor detection due to its global, all-weather coverage \cite{pulinetsLithosphereAtmosphereIonosphere2011,wuGEOSSbasedThermalParameters2012}, its operational implementation has been stalled for decades by a fundamental challenge: the signal ambiguity problem \cite{troninRemoteSensingEarthquakes2006,tramutoliRobustSatelliteTechniques2013}.

The signal ambiguity problem has emerged as the central roadblock preventing operational satellite-based earthquake monitoring. Despite five decades of research investment and technological advancement, this fundamental challenge persists: identical-appearing microwave anomalies can precede either catastrophic earthquakes or benign environmental variations, while many major earthquakes occur without any distinguishable precursory signals. This ambiguity has led to an uncomfortable reality—while individual case studies report promising anomalies before specific earthquakes, systematic global analyses fail to establish reliable predictive patterns. The result is a field rich in anecdotal evidence but lacking operational capability \cite{gellerEarthquakesCannotPredicted1997,cuiSatelliteThermalInfrared2019,contiSystematicReviewMetaAnalysis2021,ciceroneSystematicCompilationEarthquake2009,jordanOperationalEarthquakeForecasting2011}.

Recent advances in machine learning have transformed many Earth observation applications\cite{reichsteinDeepLearningEarth2019,zhuDeepLearningRemote2017}, yet earthquake precursor detection remains stubbornly resistant to these approaches. The reason, we argue, lies not in the sophistication of the algorithms but in a fundamental methodological assumption: that a universal detection method can work across Earth's diverse surface environments. This "one-size-fits-all" approach amplifies the signal ambiguity problem by treating signals from ocean surfaces, dense forests, and arid deserts identically—ignoring the profound differences in how these environments modulate microwave emissions \cite{qiCharacteristicBackgroundMicrowave2023}.

To overcome this methodological roadblock, this paper proposes a comprehensive knowledge-guided AI framework designed specifically to resolve signal ambiguity through an environment-specific approach. Our work is grounded in Microwave Brightness Temperature (MBT) observations from the AMSR-2 satellite\cite{kawanishiAdvancedMicrowaveScanning2003}, a data source with unique advantages for this task. MBT offers all-weather monitoring, penetrates surface materials to potentially capture stress-induced subsurface changes \cite{maoImpactCompressiveStress2020,takanoExperimentTheoreticalStudy2009}, and its multi-frequency nature allows for depth-resolved analysis \cite{njokuRetrievalLandSurface1999,guptaMicrowaveEmissionScattering2014}. Our framework leverages these advantages by first classifying Earth's surface into distinct environmental zones. Then, instead of relying on "black-box" learning, we use association rule mining to discover reliable, environment-specific precursor signatures (or "fingerprints"). This knowledge is then integrated into a novel Weight-Enhanced Feature-Tailored Transformer (WE-FTT) to guide its focus, fundamentally changing its ability to distinguish true precursors from environmental noise.

\begin{itemize}
\item \textbf{Environment-specific analysis} -- We classify Earth's surface into five distinct zones based on microwave radiative properties, enabling tailored precursor detection that accounts for diverse environmental conditions. This approach moves beyond uniform global methods to respect the physical differences in signal propagation across different landscapes.

\item \textbf{Knowledge-guided deep learning} -- We develop the Weight-Enhanced Feature-Tailored Transformer (WE-FTT), which integrates domain knowledge through pre-computed frequency importance weights derived from association rule mining. This design explicitly incorporates geophysical understanding into the model architecture rather than relying solely on end-to-end learning.

\item \textbf{Large-scale validation} -- We evaluate our approach on a substantial dataset comprising 346.56 million MBT measurements across 154 major earthquakes (M$\ge$7.0) from 2013-2023, providing comprehensive evidence for the method's effectiveness across diverse conditions.
\end{itemize}

The primary objectives of this study are: (i) To demonstrate that the signal ambiguity problem can be resolved through environment-specific analysis, identifying reliable MBT frequency-polarization signatures for major (M$\ge$7.0) earthquakes across five distinct surface environments. (ii) To develop the WE-FTT model that operationalizes this insight, transforming ambiguous global signals into clear environment-specific indicators. (iii) To validate that accounting for environmental heterogeneity fundamentally improves earthquake precursor detection, providing a path toward operational satellite-based monitoring. Our results aim to contribute towards more robust and reliable methods for identifying potential earthquake precursors from satellite microwave data. Our study is grounded in a massive dataset, analyzing over 346 million MBT measurements from AMSR-2 spanning a decade (2013--2023) in relation to 154 major (M$\ge$7.0) earthquakes across globally distributed environmental zones. See Figure~\ref{fig:fig1} for the framework.

% FIGURE 1 - Placed here, close to first mention
%%% <<< FIX START: Use [!htbp] for better placement >>> %%%
\begin{figure}[!htbp]
%%% <<< FIX END >>> %%%
\centering
%% IMAGE FIX: Assuming fig:fig1 corresponds to image1.png
\includegraphics[width=0.85\linewidth]{image1.png} 
\caption{Resolving signal ambiguity through environment-specific analysis: methodological framework. The workflow illustrates the integration of surface type classification, earthquake-related data sampling, clustering analysis, association rule mining, and the Weight-Enhanced Feature-Tailored Transformer (WE-FTT) model. The WE-FTT incorporates mining-derived support values as pre-computed weights that are projected and multiplied with feature embeddings prior to attention computation, enabling enhanced seismic precursor detection across diverse environmental zones. This integrated approach enables resolution of the signal ambiguity problem by dynamically adjusting detection criteria based on environmental context.}
\label{fig:fig1}
\end{figure}


%%% <<< FIX START: Add FloatBarrier to keep floats within this section >>> %%%
 \FloatBarrier 
%%% <<< FIX END >>> %%%
% FIX BOOKMARK: Changed \section* to \section
% FIX 1: No numbers will be displayed due to class file modification
\section{Results}
\subsection*{Resolving the Signal Ambiguity Through Environmental Context}

Our analysis reveals a transformative insight: the signal ambiguity that has plagued satellite earthquake monitoring for decades can be effectively resolved through environment-specific analysis. By segmenting our global dataset into five distinct environmental zones and analyzing precursor patterns independently within each zone, we discovered that what appears as intractable noise in global analyses resolves into clear, consistent signatures when properly contextualized.

Figure~\ref{fig:fig2} demonstrates this breakthrough—each environmental zone exhibits unique, highly reliable frequency-polarization combinations for detecting seismic precursors. These environment-specific signatures achieve support values approaching or reaching 1.0, indicating near-perfect reliability within their respective environments. This finding fundamentally challenges the prevailing assumption that earthquake precursors should manifest uniformly across different surface types.

\subsection{Analysis of Multi-frequency Microwave for Zone-Dependent Pre-seismic Anomaly Detection}
An initial exploratory analysis across the full earthquake catalog (M$\ge$4.8) revealed that consistent, statistically significant MBT anomalies were predominantly associated with major earthquakes (M$\ge$7.0). For smaller magnitudes, potential signals were indistinguishable from background noise at a global scale, confirming that the signal ambiguity problem is particularly acute for lower-magnitude events. We therefore focused our main analysis on M$\ge$7.0 earthquakes, where signals, though still ambiguous in conventional global analyses, possess sufficient strength to become distinguishable through our environment-specific framework. The integrated methodological framework presented in Figure~\ref{fig:fig1} enabled comprehensive analysis of AMSR-2 microwave brightness temperature data across five distinct environmental zones has revealed characteristic pre-seismic anomaly patterns through specific frequency-polarization combinations, demonstrating significant detection capabilities for seismic precursor signals. Through comprehensive frequent itemset mining analysis, we have identified environment-specific anomaly signatures that achieve perfect or near-perfect support values, indicating robust seismic precursor detection capabilities across diverse landscape conditions (Figure~\ref{fig:fig2}).

In marine environments (Zone A), distinct pre-seismic anomalies were most effectively captured by the combination of 89 GHz H-polarization (anomaly range $\sim$219--253 K) and 36.5 GHz V-polarization (123--248 K), achieving perfect detection reliability (support = 1.0000). A secondary anomaly signature comprising 23.8 GHz H-polarization (177--235 K) and 36.5 GHz H-polarization (109--209 K) also showed near-perfect reliability (support = 0.9923). The overlapping temperature range ($\sim$177--209 K) between these channels defines a critical detection window for marine earthquake precursors.

Humid forest zones (Zone B) exhibited unique anomaly signatures, with optimal detection achieved through 89 GHz V-polarization (98--295 K) combined with 36.5 GHz V-polarization (140--297 K), yielding perfect support. Detection reliability showed a systematic slight decrease when additional frequency channels were included: for example, including 10.65 GHz H-polarization (169--385 K) maintained 0.5154 support, while adding 6.9 GHz H-polarization (164--388 K) gave 0.5151. This minimal support difference ($\Delta$support $\approx$ 0.0003) quantifies the effect of dense vegetation on precursor signal detectability.

Dry forest environments (Zone C) revealed seismic precursor signatures through 36.5 GHz H-polarization (121--207 K) combined with 10.65 GHz V-polarization (160--220 K), achieving near-perfect detection reliability. The stability of the 36.5 GHz H-polarization anomaly range (standard deviation <0.1 K) indicates a consistent precursor signature. Secondary combinations including 6.9 GHz V-polarization (152--216 K) also achieved very high reliability (support = 0.9976), with anomalies concentrated around 159--207 K.

Wetland zones (Zone D) demonstrated complex anomaly patterns. Optimal detection involved various combinations incorporating 6.9 GHz H-polarization (73--166 K). Anomaly signatures showed a stratified pattern: low-frequency H-polarization exhibited broad detection ranges ($\Delta T \approx 93$ K), mid-frequency V-polarization had intermediate detection windows (e.g., $\Delta T \approx 164$ K for 23.8 GHz), and high-frequency channels provided complementary detection characteristics (e.g., $\Delta T \approx 214$ K for 36.5 GHz V-polarization). This stratification suggests multi-layered precursor processes in wetlands.

% FIGURE 2 
%%% <<< FIX START: Use [!htbp] for better placement >>> %%%
\begin{figure}[!htbp]
%%% <<< FIX END >>> %%%
\centering
%% IMAGE FIX: Assuming fig:fig2 corresponds to image2.png
\includegraphics[width=0.85\linewidth]{image2.png}
\caption{Breakthrough in signal disambiguation: environment-specific precursor signatures achieving near-perfect reliability. Primary combinations (blue) and secondary combinations (green) highlight varying sensitivities, with each bar labeled with the corresponding frequency-polarization combination (H: Horizontal, V: Vertical) to indicate the most effective configurations in each zone.}
\label{fig:fig2}
\end{figure}
 
In arid zones (Zone E), seismic precursor detection was optimized through the combination of 23.8GHz H-polarization (189.04-240.88K) and 36.5GHz V-polarization (103.18-246.57K), achieving perfect reliability while individual channels showed significantly lower detection capabilities (0.0693 for 23.8GHz V). Low-frequency channels exhibited broad anomaly detection ranges ($\Delta T \approx 154$K for 6.9GHz H) despite limited reliability ($\le$0.005), indicating complex subsurface precursor mechanisms.
 
 
\subsection{Terrain-Specific Microwave Sensitivity and Optimal Channel Combinations}
To evaluate AMSR-2's multi-frequency microwave sensitivity to seismic anomalies, we conducted a comprehensive analysis of mean support values across diverse terrain types. The study categorized surface types into five zones based on vegetation coverage and soil moisture content: marine areas (Zone A), high vegetation-high soil moisture areas (Zone B), high vegetation-low soil moisture areas (Zone C), low vegetation-high soil moisture areas (Zone D), and low vegetation-low soil moisture areas (Zone E). We analyzed horizontal and vertical polarization channels at 6.9 GHz, 10.65 GHz, 23.8 GHz, 36.5 GHz, and 89.0 GHz frequencies, calculating mean support values across all frequent itemsets to assess seismic anomaly detection capabilities.

% FIX REF: Added reference to Table 1 and Figure 3
Analysis of mean support values revealed distinctive patterns across frequency bands and polarizations (see Table~\ref{tab:table1} and Figure~\ref{fig:fig3}). At 6.9 GHz, horizontal polarization demonstrated varying effectiveness across terrain types, with highest support values in Zone D (0.9348, MBT: 166.36-655.35K), followed by Zone C (0.5716, MBT: 73.61-145.63K). Vertical polarization at this frequency showed similar patterns but with generally higher MBT ranges, particularly in Zone D (0.9219, MBT: 214.9-655.35K). Zone B showed moderate support values (0.2688, MBT: 163.66-388.18K), while Zone A exhibited minimal sensitivity (0.0074, MBT: 41.88-168.06K).

The 10.65 GHz band exhibited enhanced detection capability, especially in wetland areas (Zone D), where horizontal polarization achieved a support value of 0.9306 (MBT: 171.55-655.35K) and vertical polarization reached 0.9229 (MBT: 219.56-655.35K). Notably, dry forest regions (Zone C) showed distinct polarization preferences, with vertical polarization achieving 0.6220 (MBT: 159.63-220.37K). Zones A and B maintained relatively low support values, with Zone B showing slightly better performance (0.2689, MBT: 168.79-385.17K).

Higher frequency bands demonstrated more pronounced environment-specific responses. The 23.8 GHz horizontal polarization showed exceptional performance in Zone D (0.9044, MBT: 201.73-655.35K), while maintaining moderate effectiveness in Zone E (0.5075, MBT: 189.04-240.88K). Zone B showed improved response at this frequency, reaching 0.3016 (MBT: 104.42-182.47K). The 36.5 GHz band achieved the highest overall support values, particularly in Zone D where horizontal polarization reached 0.9713 (MBT: 209.48-655.35K) and vertical polarization achieved 0.8738 (MBT: 139.48-301.56K). The 89.0 GHz band showed unique capabilities in arid regions (Zone E), with horizontal polarization achieving 0.7380 (MBT: 64.46-215.3K), while Zone A reached its maximum sensitivity at this frequency (0.5078, MBT: 219.34-252.97K).


% Table 1 Corrected, spaced and bolded
%%% <<< FIX START: Use [!htbp] for better placement >>> %%%
\begin{table}[!htbp]
%%% <<< FIX END >>> %%%
\centering
% Corrected the typo "indicate" to "indicates"
\caption{\label{tab:table1}Segmented MBT ranges and corresponding mean support values for dual-polarization microwave observations across distinct terrain zones, the "---" symbol indicates in some cells not applicable. Range values are in Kelvin (K).}
\resizebox{\textwidth}{!}{% Resize table to fit text width - use with caution
 % Add a bit vertical space between rows
 \renewcommand{\arraystretch}{1.1}
\begin{tabular}{@{}llccccc@{}}
\toprule
% Header Structure matching Image 1 more closely
      &   & \multicolumn{5}{c}{Mean Support Value (MBT segmentation range)} \\
 \cmidrule(lr){3-7}
\multicolumn{2}{l}{Freq. (GHz)}  & Zone A & Zone B & Zone C & Zone D & Zone E \\
\midrule
% Freq 6.9 Block: H max_rows=2, V max_rows=2. Total = 4 rows.
\multirow{4}{*}{6.9} 
    & \multirow{2}{*}{H} 
        & 0.0074\,(41.8--168.1)  & 0.2688\,(163.7--388.2) & 0.5716\,(73.6--145.6)  & 0.9348\,(166.4--655.4) & 0.4038\,(168.9--655.4) \\
    % ADD SPACE [6pt] AFTER THIS LINE (last row of 6.9 H)
    &   & ---                   & 0.2201\,(73.4--163.7)  & 0.4408\,(226.8--382.8) & 0.8015\,(73.3--166.4)  & 0.0042\,(14.3--168.9) \\[6pt] 
    & \multirow{2}{*}{V} 
         % 0.3095 (252.2--388.1) <<< BOLD ADDED HERE (6.9V, Zone B)
        & 0.0076\,(57.1--219.2)  & \textbf{0.3095\,(252.2--388.1)} & 0.6219\,(152.3--215.6) & 0.9219\,(214.9--655.4) & 0.4038\,(218.6--655.4) \\
    &   & ---                   & 0.2193\,(138.1--205.0) & 0.4743\,(215.6--384.4) & 0.7759\,(139.7--214.9) & 0.0043\,(47.4--218.6) \\
\midrule
% Freq 10.65 Block: H max_rows=2, V max_rows=2. Total = 4 rows.
\multirow{4}{*}{10.65} 
    & \multirow{2}{*}{H} 
        & 0.0075\,(68.9--173.7)  & 0.2689\,(168.8--385.2) & 0.5578\,(79.0--130.8)  & 0.9306\,(171.6--655.4) & 0.4038\,(173.4--655.4) \\
     % ADD SPACE [6pt] AFTER THIS LINE (last row of 10.65 H)
    &   & ---                   & 0.2202\,(78.9--168.8)  & ---                   & 0.8014\,(78.9--171.5)  & 0.0041\,(79.1--173.4) \\[6pt]
    & \multirow{2}{*}{V} 
        & 0.0075\,(121.3--223.9) & 0.2198\,(146.2--212.0) & 0.6220\,(159.6--220.4) & 0.9229\,(219.6--655.4) & 0.4038\,(222.8--655.4) \\
    &   & ---                   & ---                   & 0.4748\,(220.4--385.4) & 0.7690\,(148.0--219.6) & 0.0042\,(146.6--222.8) \\
\midrule
% Freq 23.8 Block: H max_rows=3, V max_rows=2. Total = 5 rows.
\multirow{5}{*}{23.8}  
    & \multirow{3}{*}{H} 
        % 0.5124 (177.2--234.6) <<< BOLD ADDED HERE (23.8H, Zone A)
        & \textbf{0.5124\,(177.2--234.6)} & 0.3016\,(104.4--182.5) & 0.4676\,(233.8--292.5) & 0.9044\,(201.7--655.4) & 0.5075\,(189.0--240.9) \\
    &   & 0.0132\,(103.3--177.2) & 0.2506\,(237.0--292.1) & 0.2179\,(180.3--233.8) & ---                   & 0.3366\,(104.8--189.0) \\
    % ADD SPACE [6pt] AFTER THIS LINE (last row of 23.8 H)
    &   & ---                   & 0.1465\,(182.5--237.0) & ---                   & ---                   & --- \\[6pt]
    & \multirow{2}{*}{V} 
        & 0.0183\,(241.0--655.3) & 0.3029\,(157.2--256.0) & 0.5043\,(257.4--302.6) & 0.8738\,(201.7--655.4) & 0.5224\,(232.9--261.4) \\
    &   & 0.0069\,(162.9--241.0) & 0.1549\,(256.0--298.3) & 0.0497\,(227.9--257.4) & ---                   & 0.0426\,(168.1--232.9) \\
\midrule
% Freq 36.5 Block: H max_rows=2, V max_rows=2. Total = 4 rows.
\multirow{4}{*}{36.5}  
    & \multirow{2}{*}{H} 
        % 0.7028 (121.4--207.1) <<< BOLD ADDED HERE (36.5H, Zone C)
        % 0.9713 (209.5--655.4) <<< BOLD ADDED HERE (36.5H, Zone D)
        & 0.0227\,(109.3--209.3) & 0.1745\,(121.7--185.1) & \textbf{0.7028\,(121.4--207.1)} & \textbf{0.9713\,(209.5--655.4)} & 0.4052\,(210.9--655.4) \\
     % ADD SPACE [6pt] AFTER THIS LINE (last row of 36.5 H)
    &   & ---                   & ---                   & 0.5494\,(207.1--290.9) & 0.7824\,(111.5--209.5) & 0.0004\,(102.5--210.9) \\[6pt]
    & \multirow{2}{*}{V} 
        & 0.0228\,(122.7--247.6) & 0.3054\,(139.6--297.2) & 0.5412\,(148.4--244.3) & 0.8738\,(139.5--301.6) & 0.4079\,(103.2--246.6) \\
    &   & ---                   & ---                   & 0.4229\,(244.3--300.6) & ---                   & 0.0305\,(246.6--655.4) \\
\midrule
% Freq 89.0 Block: H max_rows=3, V max_rows=2. Total = 5 rows.
\multirow{5}{*}{89.0}  
    & \multirow{3}{*}{H} 
         % 0.7380 (64.5--215.3) <<< BOLD ADDED HERE (89.0H, Zone E)
        & 0.5078\,(219.3--253.0) & 0.3053\,(94.8--212.0)  & 0.5567\,(217.3--250.0) & 0.8738\,(80.6--295.9)  & \textbf{0.7380\,(64.5--215.3)}  \\
    &   & 0.0261\,(253.0--644.4) & 0.1638\,(246.1--292.9) & 0.0340\,(250.0--293.4) & ---                   & 0.0159\,(215.3--250.0) \\
    % ADD SPACE [6pt] AFTER THIS LINE (last row of 89.0 H)
    &   & 0.0023\,(72.6--219.3)  & 0.1335\,(212.1--246.1) & ---                   & ---                   & ---\\[6pt]
    & \multirow{2}{*}{V} 
        & 0.0179\,(270.7--655.4) & 0.3054\,(98.0--295.4)  & 0.5741\,(260.8--298.5) & 0.8738\,(82.8--298.8)  & 0.2657\,(65.2--231.2)  \\
    &   & 0.0018\,(254.1--570.7) & ---                   & 0.0001\,(229.1--260.8) & ---                   & 0.0647\,(231.3--263.3) \\
\bottomrule
\end{tabular}
} % End resizebox
\end{table}


% FIGURE 3
%%% <<< FIX START: Use [!htbp] for better placement >>> %%%
\begin{figure}[!htbp]
%%% <<< FIX END >>> %%%
\centering
%% IMAGE FIX: Assuming fig:fig3 corresponds to image3.png
\includegraphics[width=0.85\linewidth]{image3.png}
\caption{Illustration of the polarization dependence of microwave response characteristics across different terrain zones. The mean support values were evaluated as a function of frequency for (a) horizontal and (b) vertical polarization. The optimal microwave brightness temperature (MBT) ranges and their associated support values were determined for (c) horizontal and (d) vertical polarization configurations.}
\label{fig:fig3}
\end{figure}

Multiple frequency combinations demonstrated enhanced detection capabilities in specific terrains. In Zone D, the combination of 36.5 GHz and 23.8 GHz horizontal polarization channels provided optimal detection with support values consistently above 0.90. Zone C showed best response to combined 36.5 GHz horizontal polarization (0.7028, MBT: 121.4-207.09K) and 10.65 GHz vertical polarization (0.6219, MBT: 152.28-215.62K) observations. Zone E exhibited optimal sensitivity with 89.0 GHz horizontal polarization (0.7380, MBT: 64.46-215.3K) paired with 23.8 GHz vertical polarization (0.5224, MBT: 232.9-261.39K). Zones A and B showed limited improvement with frequency combinations, suggesting fundamental physical limitations in these environments.


\subsection{Model Performance Evaluation}
The proposed Weight-Enhanced Feature-Tailored Transformer (WE-FTT) architecture was systematically evaluated through comprehensive classification analysis and comparative performance assessment against established machine learning models. This section presents the detailed results of these evaluations, demonstrating the effectiveness of the frequency itemset-based weight optimization approach.


\subsubsection{Classification Performance of the WE-FTT Model}
The classification capabilities of the WE-FTT model were evaluated using a confusion matrix analysis across all frequency-polarization combinations, as shown in Figure~\ref{fig:fig4}. The model demonstrates remarkable classification accuracy, achieving an average of 84.2\% correct classifications across all microwave channels. This high accuracy is particularly significant given the complex nature of the classification task, which involves distinguishing between multiple frequency-polarization combinations in the presence of environmental variability and potential seismic anomalies.

% FIGURE 4
%%% <<< FIX START: Use [!htbp] for better placement >>> %%%
\begin{figure}[!htbp]
%%% <<< FIX END >>> %%%
\centering
%% IMAGE FIX: Assuming fig:fig4 corresponds to image4.png
\includegraphics[width=0.85\linewidth]{image4.png}
\caption{Brightness Temperature Polarization Classification Matrix from the Weight-Enhanced FT-Transformer (WE-FTT) model across five AMSR-2 frequency bands (6.9, 10.65, 23.8, 36.5, and 89.0 GHz). Each cell displays the sample count and row percentage, with color coding indicating classification patterns: navy blue for correct classifications (diagonal), light blue for H-V polarization confusion within the same frequency, and coral red for cross-frequency confusions. The high diagonal values (averaging 84.2\%) demonstrate the model's exceptional classification capability across all frequency-polarization combinations, with particularly strong performance in the 36.5 GHz and 89.0 GHz bands.}
\label{fig:fig4}
\end{figure}

The confusion matrix reveals several important patterns in the model's classification behavior. First, the diagonal elements (navy blue) show consistently high accuracy across all channels, with particularly strong performance in the 36.5 GHz (84.2\%) and 89.0 GHz (84.2\%) bands. Second, the light blue cells, representing H-V polarization confusion within the same frequency, show minimal misclassification (average 3.1\%), indicating the model's strong capability to distinguish between polarization states. This is particularly important for seismic anomaly detection, as polarization sensitivity provides critical information about surface structural changes associated with seismic activity.
The coral-colored cells represent cross-frequency confusion, with color intensity corresponding to the percentage of misclassifications. Notably, the confusion predominantly occurs between adjacent frequency bands (e.g., between 6.9 GHz and 10.65 GHz), which is physically reasonable given the spectral proximity of these channels. The model shows minimal confusion between spectrally distant channels (e.g., between 6.9 GHz and 89.0 GHz), with an average cross-band confusion of only 1.9\%. This selective confusion pattern demonstrates the model's ability to capture the physical relationships between frequency channels while maintaining discriminative power between spectrally distinct measurements.

% FIGURE 5
%%% <<< FIX START: Use [!htbp] for better placement >>> %%%
\begin{figure}[!htbp]
%%% <<< FIX END >>> %%%
\centering
%% IMAGE FIX: Assuming fig:fig5 corresponds to image5.png
 \includegraphics[width=0.85\linewidth]{image5.png}
\caption{Model performance comparison across six evaluation metrics. (a) Matthews Correlation Coefficient (MCC), (b) F1 Score, (c) Accuracy, (d) Precision, (e) Cohen's Kappa, and (f) Recall. The Weight-Enhanced FT-Transformer (red) consistently outperforms all baseline models across all metrics, with RandomForest (navy blue) showing second-best performance. The significant performance gap between the proposed WE-FTT model and traditional approaches highlights the effectiveness of the frequency itemset-based weight optimization methodology.}
\label{fig:fig5}
\end{figure}

\subsubsection{Comparative Analysis of Model Performance}
To rigorously evaluate the effectiveness of the proposed WE-FTT approach, we conducted a comprehensive performance comparison against five established machine learning models: RandomForest, LightGBM, TabNet, CatBoost, and XGBoost. Each model was trained on identical dataset splits and evaluated using six complementary performance metrics: Matthews Correlation Coefficient (MCC), F1 Score, Accuracy, Precision, Cohen's Kappa, and Recall. Figure~\ref{fig:fig5} presents the detailed comparison across these metrics, revealing consistent performance advantages for the WE-FTT architecture.

As shown in Figure~\ref{fig:fig5}, the proposed WE-FTT model achieved superior performance across all evaluation metrics. The WE-FTT achieved an MCC of 0.84, F1 score of 0.82, accuracy of 0.84, precision of 0.80, Cohen's Kappa of 0.82, and recall of 0.84. These results represent substantial improvements over the next best model (RandomForest), which achieved an MCC of 0.74, F1 score of 0.70, accuracy of 0.72, precision of 0.71, Cohen's Kappa of 0.68, and recall of 0.72. This performance gap is especially significant considering that MCC is particularly sensitive to imbalanced classification scenarios characteristic of seismic anomaly detection tasks. The consistent performance advantages across diverse metrics demonstrate the WE-FTT's robust generalization capabilities across different evaluation criteria. The significantly higher MCC score, in particular, highlights the WE-FTT's enhanced capability in handling the imbalanced nature and inherent signal ambiguity of the seismic precursor detection task, where baseline models likely struggle more to differentiate true positives from complex background noise.

Statistical significance testing, as illustrated in Figure~\ref{fig:fig6}a, confirms the reliability of these performance differences. Bootstrap resampling with 1,000 iterations generated 95\% confidence intervals for the aggregated performance scores, revealing statistically significant differences between the WE-FTT (0.8271) and RandomForest (0.7119, p<0.01), as well as between subsequent model pairs in the performance ranking. This statistical validation reinforces the conclusion that the WE-FTT's performance advantages are systematic rather than artifacts of sampling variation.
The multi-dimensional performance profile visualization in Figure~\ref{fig:fig6}b further illustrates the WE-FTT's balanced excellence across all metrics. While some baseline models showed uneven performance profiles with strengths in certain metrics and weaknesses in others, the WE-FTT maintained consistently high values across all dimensions, creating a more uniformly expanded performance polygon. This balanced profile indicates that the model achieves its high overall performance without sacrificing specific aspects of classification quality.

Principal Component Analysis (PCA) of the performance metrics, shown in Figure~\ref{fig:fig6}c, provides additional insights into the relationships between models and evaluation criteria. The first two principal components capture 93.5\% of the total variance, with PC1 (78.2\%) primarily representing overall performance and PC2 (15.3\%) capturing the precision-recall trade-off dimension. The WE-FTT's position in the upper-right quadrant of this reduced feature space demonstrates its dominance in both dimensions, while the clustering of lower-performing models in the left region highlights the significant performance gap between the proposed approach and traditional methods.

% FIGURE 6
%%% <<< FIX START: Use [!htbp] for better placement >>> %%%
\begin{figure}[!htbp]
%%% <<< FIX END >>> %%%
\centering
%% IMAGE FIX: Assuming fig:fig6 corresponds to image6.png
 \includegraphics[width=0.85\linewidth]{image6.png}
\caption{Comprehensive model performance analysis. (a) Model ranking based on aggregated performance scores with 95\% bootstrap confidence intervals (n=1000). Statistical significance between adjacent models is indicated (*p < 0.05, **p < 0.01). The WE-FTT demonstrates statistically significant superior performance compared to all other models. (b) Radar chart displaying performance profiles across six evaluation metrics, where the FT\_Transformer (red) shows consistently higher values compared to RandomForest (blue). (c) Principal Component Analysis of model performance data, with the first two components explaining 93.5\% of variance, revealing clear separation between high-performing models (upper right) and lower-performing ones (lower left).}
\label{fig:fig6}
\end{figure}

To further examine the detailed classification patterns across different models, Figure~\ref{fig:fig7} presents the confusion matrices for all six evaluated models. The WE-FTT model (Figure~\ref{fig:fig7}a) demonstrates substantially higher diagonal values (correct classifications) compared to all other models, with an overall accuracy of 84.2\%. In contrast, the next best performer, RandomForest (Figure~\ref{fig:fig7}b), achieves only 71.9\% accuracy, with notably increased cross-frequency confusion particularly between adjacent frequency bands. The performance differential becomes even more pronounced when comparing with the lower-performing models: LightGBM (67.6\%), TabNet (63.0\%), CatBoost (58.1\%), and XGBoost (55.4\%).

A particularly noteworthy pattern emerges when examining the H-V polarization confusion (light blue cells) across different models. The WE-FTT demonstrates remarkably low in-band polarization confusion, while traditional models show significantly higher confusion rates in this category. This enhanced polarization discrimination capability can be directly attributed to the weight optimization strategy, which effectively leverages the distinctive information content of different polarization channels based on their relevance to seismic anomaly detection.
The confusion matrices also reveal that lower-performing models exhibit increased misclassification between spectrally distant channels. This pattern suggests that traditional models struggle to capture the complex physical relationships between frequency channels, while the WE-FTT's attention mechanism effectively models these interdependencies through its weight-enhanced feature interactions.

% FIGURE 7
%%% <<< FIX START: Use [!htbp] for better placement >>> %%%
\begin{figure}[!htbp]
%%% <<< FIX END >>> %%%
\centering
%% IMAGE FIX: Assuming fig:fig7 corresponds to image7.png
 \includegraphics[width=0.85\linewidth]{image7.png}
\caption{Confusion matrices showing classification performance across different models, with the percentage in parentheses indicating the overall classification accuracy of each model: (a) WE-FTT (84.2\%), (b) RandomForest (71.9\%), (c) LightGBM (67.6\%), (d) TabNet (63.0\%), (e) Catboost (58.1\%), and (f) Xgboost (55.4\%). Each matrix displays correct classifications (dark blue), H-V polarization confusion (light blue), and cross-frequency confusion (coral red). The results demonstrate that the WE-FTT model achieves significantly higher correct classification rates compared to all baseline models, with RandomForest showing the second-best performance.}
\label{fig:fig7}
\end{figure}

\subsubsection{Relationship to Frequency Itemset Mining Results}
%% Note: Original text mentioned Section 4.1, adjusted to "itemset mining results"
The superior performance of the WE-FTT model can be directly linked to the frequency itemset mining results presented earlier. The model's pre-computed weight approach effectively leverages the identified optimal frequency-polarization combinations for each environmental zone. These weights, derived from the support values identified in the itemset mining analysis, are processed through a dedicated projection pathway before being combined with feature embeddings through element-wise multiplication. For instance, in Zone D (Wetland), the model assigns higher weights to the 36.5 GHz and 23.8 GHz horizontal polarization channels, which were identified as having exceptionally high support values (0.9713 and 0.9044 respectively) in the itemset mining analysis. Similarly, for Zone C (Dry Forest), the model emphasizes the 36.5 GHz horizontal polarization (support value: 0.7028) and 10.65 GHz vertical polarization (support value: 0.6220) channels.
This superior ability to resolve ambiguous signals stems directly from integrating the mined knowledge. By assigning pre-computed weights based on rules identifying consistent pre-seismic signatures (e.g., high support for 36.5 GHz H and 10.65 GHz V in Zone C), the WE-FTT effectively learns the subtle, environment-specific decision boundary between seismic and non-seismic states, enabling more reliable classification even when individual channel readings might otherwise be misleading. By focusing attention on the most informative frequency-polarization combinations for each environmental context, the WE-FTT achieves both improved classification accuracy and enhanced physical interpretability of the learned patterns.
The significant performance improvements observed in the comparative analysis thus validate the effectiveness of the frequency itemset-based weight optimization methodology. By systematically mining the relationships between frequency channels and their association with seismic activity across different environmental conditions, and then integrating this knowledge into the model's attention mechanism, the proposed approach successfully addresses the complex challenges of microwave-based seismic anomaly detection.

\subsection{Ablation Study}
To understand the mechanistic basis of our model's superior performance, we systematically evaluated the contribution of each architectural component through comprehensive ablation experiments. This analysis not only validates our design choices but also reveals fundamental insights into how different frequency channels contribute to seismic precursor detection.
\subsubsection{Architectural Component Hierarchy}
Our ablation experiments across 18 variants revealed a clear hierarchy of component importance (Figure~\ref{fig:fig8}). The weight-enhanced dual projection pathway emerged as the cornerstone of our architecture---its removal caused catastrophic performance collapse (MCC: 0.84$\rightarrow$0.42), representing nearly 50\% degradation. This dramatic impact confirms that pre-computed frequency importance weights cannot be effectively learned through end-to-end training alone. Feature projection proved similarly critical (41.4\% impact), while attention mechanisms showed more nuanced effects: residual connections (35\% impact) outweighed multi-head attention benefits (28\% impact), suggesting that stable gradient flow trumps attention diversity for geophysical signal processing. Surprisingly, position encoding---fundamental in language models---contributed minimally (<6\%), indicating that spatial relationships in gridded MBT data differ fundamentally from sequential dependencies.

% FIGURE 8
%%% <<< FIX START: Use [!htbp] for better placement >>> %%%
\begin{figure}[!htbp]
%%% <<< FIX END >>> %%%
\centering
%% IMAGE FIX: Assuming fig:fig8 corresponds to image8.png
 \includegraphics[width=0.85\linewidth]{image8.png}
\caption{Ablation study results across six key architectural components: (a) Input Projection shows performance with different feature and weight projection approaches; (b) Attention Mechanism compares residual connections and attention head configurations; (c) Feature Fusion evaluates alternative fusion strategies; (d) Loss Function contrasts cross-entropy and dynamic focal loss implementations; (e) Position Encoding examines the impact of different positional information strategies; and (f) Training Strategy analyzes warmup and learning rate decay effects. Each bar represents performance scores across six evaluation metrics: MCC, F1 Score, Accuracy, Precision, Kappa, and Recall.}
\label{fig:fig8}
\end{figure}

\subsubsection{Performance Signatures Across Metrics}
Different architectural modifications produced distinct performance signatures across evaluation metrics (Figure~\ref{fig:fig9}). Weight projection removal caused asymmetric degradation: Cohen's Kappa plummeted 57.7\% while Precision dropped only 44.5\%, revealing that weight integration specifically enhances cross-class discrimination---crucial for distinguishing genuine precursors from environmental noise. Feature fusion variants maintained more balanced profiles, with alternative fusion achieving near-baseline MCC (0.816) while selectively compromising F1 Score (0.755). This metric-specific sensitivity provides a roadmap for future architectural refinements: components showing uniform degradation (weight projection) are irreplaceable, while those with selective impacts (fusion strategies) offer optimization opportunities.

% FIGURE 9
%%% <<< FIX START: Use [!htbp] for better placement >>> %%%
\begin{figure}[!htbp]
%%% <<< FIX END >>> %%%
\centering
%% IMAGE FIX: Assuming fig:fig9 corresponds to image9.png
\includegraphics[width=1\linewidth]{image9.png}
\caption{Radar visualization of model performance metrics across different ablation variants. Six distinct radar charts are shown, each representing a specific evaluation metric perspective: (a) MCC (red), (b) F1 Score (dark blue), (c) Accuracy (light blue), (d) Precision (green), (e) Kappa (purple), and (f) Recall (orange). Each radar plot illustrates how different architectural modifications affect model performance across all evaluation dimensions, with the radial distance from center representing the metric value (higher is better). The visualization reveals consistent patterns of performance degradation, particularly severe for the weight projection removal variants.}
\label{fig:fig9}
\end{figure}

\subsubsection{Component-Channel Coupling}
Integrated analysis uncovered a profound connection between architectural components and physical signal characteristics (Figure~\ref{fig:fig10}). Component impact quantification (Figure~\ref{fig:fig10}a) established that input projection mechanisms account for over 70\% of total model performance, far exceeding traditional attention mechanisms. Multi-metric analysis (Figure~\ref{fig:fig10}b) revealed specialized roles: input projections govern overall accuracy, attention mechanisms control error distributions, while fusion strategies balance class predictions. The effectiveness-complexity trade-off (Figure~\ref{fig:fig10}c) identified unexpected efficiency gains---learnable position embeddings slightly improved performance while reducing computational overhead, challenging conventional transformer design wisdom.
Most revealing was the class-specific sensitivity analysis (Figure~\ref{fig:fig10}d), which demonstrated remarkable alignment between architectural importance and physical significance. Frequency channels 0, 3, 4, and 9---precisely those identified as optimal for seismic detection in our mining analysis---showed 3-fold higher sensitivity to weight projection removal. This direct correspondence validates our central hypothesis: integrating domain knowledge through architectural design enables detection of subtle geophysical patterns invisible to conventional approaches.

% FIGURE 10
%%% <<< FIX START: Use [!htbp] for better placement >>> %%%
\begin{figure}[!htbp]
%%% <<< FIX END >>> %%%
\centering
%% IMAGE FIX: Assuming fig:fig10 corresponds to image10.png
\includegraphics[width=1\linewidth]{image10.png}
\caption{Comprehensive ablation analysis of the WE-FTT model: (a) Component Impact on Performance quantifies MCC reduction when removing each component, with weight projection showing the highest impact (48.9\%); (b) Model Component Impact Analysis evaluates components across three metrics: MCC Reduction Rate, Error Asymmetry, and Column Bias; (c) Component Effectiveness Analysis plots MCC against computational complexity, revealing optimal efficiency in the upper-right quadrant; (d) Class Sensitivity Analysis demonstrates how component removal differentially affects performance across feature classes, with classes 0, 3, 4, and 9 showing the highest sensitivity to architecture changes.}
\label{fig:fig10}
\end{figure}

\subsubsection{Implications for Geophysical Signal Processing} 
These findings establish three fundamental principles for AI-driven geophysical analysis: First, domain knowledge integration through specialized architectural pathways outperforms generic deep learning. Second, the hierarchy of component importance mirrors the physical hierarchy of signal generation---from lithospheric stress to surface emissions. Third, the tight coupling between mined frequency patterns and architectural sensitivity suggests that our model has learned physically meaningful representations rather than statistical artifacts.
The ablation study thus transcends technical validation, revealing how architectural innovations can encode geophysical understanding into neural networks. By demonstrating that pre-computed weights based on seismic-frequency associations are irreplaceable by learned parameters, we establish a new paradigm for knowledge-guided deep learning in Earth observation.

%%% <<< FIX START: Add FloatBarrier to keep floats within this section >>> %%%
 \FloatBarrier 
%%% <<< FIX END >>> %%%
% FIX BOOKMARK: Changed \section* to \section
% FIX 1: No numbers will be displayed due to class file modification
\section{Discussion}
\subsection{From Ambiguity to Clarity: A Paradigm Shift in Satellite Earthquake Monitoring}
Our findings demonstrate a robust pathway to resolving the signal ambiguity problem that has prevented operational satellite-based earthquake monitoring for half a century. The key insight is deceptively simple yet profound: reliable precursor signatures exist, but they are environment-specific rather than universal. By abandoning the search for a single, global precursor pattern and instead embracing environmental heterogeneity, we have transformed an ambiguous signal into a clear indicator of seismic preparation \cite{qiSpatiotemporallyWeightedTwostep2020,qiMicrowaveBrightnessTemperature2022,wuIdentifyingSeismicAnomalies2024}.

This paradigm shift has immediate practical implications. The Weight-Enhanced Feature-Tailored Transformer, achieving an MCC of 0.84 compared to 0.74 for the best conventional approach, demonstrates that accounting for environmental context is not merely an incremental improvement but a fundamental requirement for reliable detection. The 13.5\% performance gain represents the difference between a system plagued by false alarms and missed events versus one approaching operational reliability.

\subsection{Physical Mechanisms and Theoretical Implications}
The observed frequency-dependent anomaly patterns provide compelling evidence for multiple interconnected seismic preparation mechanisms operating across different environmental contexts. In wetland and arid zones, the broad anomaly detection ranges in low-frequency channels ($\Delta T$: 93-154K) strongly support the P-hole activation hypothesis, wherein stress-induced positive charge carriers migrate from deep crustal sources to the surface, modifying subsurface dielectric properties \cite{freundPreearthquakeSignalsUnderlying2011, freundTimeresolvedStudyCharge, maoImpactCompressiveStress2020}. The depth-dependent sensitivity---with 6.9 GHz channels penetrating deeper than higher frequencies---enables quasi-tomographic monitoring of these charge carrier distributions. This mechanism is particularly pronounced in wetlands where high soil moisture amplifies dielectric contrasts, explaining the exceptional performance of low-frequency H-polarization combinations \cite{jingChangesSurfaceWater2022}.
Marine environments exhibit distinct precursor signatures through high-frequency channels, consistent with theoretical models of thermal energy transfer from submarine fault zones. The 89 GHz H-polarization sensitivity likely captures microscale roughness changes induced by seafloor deformation or gas seepage, while 36.5 GHz V-polarization responds to broader thermal anomalies propagating through the water column \cite{liuGeneralFeaturesMultiparameter2023}. The systematic polarization dependence observed across all zones---with H-polarization dominating in structured environments and V-polarization excelling in moisture-rich conditions---reveals fundamental differences in how pre-seismic electromagnetic signals interact with various surface media. This polarization sensitivity provides a powerful discriminator between genuine precursors and environmental noise, as meteorological variations typically affect both polarizations similarly \cite{lixinProgressesPossibleFrontiers2022}.

Our multi-frequency, multi-polarization framework significantly advances beyond previous single-channel approaches. While Wu et al. \cite{wuIdentifyingSeismicAnomalies2024} employed wavelet maxima analysis on individual frequency bands and Liu et al. \cite{liuPreearthquakeMBTAnomalies2023} focused on threshold-based detection in specific regions, our method captures the synergistic information contained in frequency-polarization combinations. The association rule mining results demonstrate that certain channel combinations achieve perfect detection reliability only when analyzed together, suggesting nonlinear interactions between different penetration depths and polarization states. This finding addresses the fundamental challenge identified by Maeda and Takano \cite{maedaDiscriminationLocalFaint2008}: discriminating subtle local seismic changes from overwhelming ambient fluctuations. By establishing environment-specific detection windows and adaptive weighting schemes, our methodology provides the first quantitative framework for optimizing signal-to-noise ratios across Earth's diverse landscapes.

\subsection{Limitations and Critical Considerations}
Despite these advances, several factors warrant careful consideration when interpreting our results. The focus on M$\ge$7.0 earthquakes, while justified by preliminary analyses showing inconsistent MBT anomalies for smaller events in global-scale data, necessarily limits operational applicability \cite{jingLandAtmosphereMeteorological2019}. Regional studies with higher spatial resolution might reveal consistent precursors for moderate-magnitude events, particularly in areas with specific geological or environmental conditions that amplify pre-seismic signals. The classical Dobrovolsky radius formula (R = $10^{0.43M}$ km) may not adequately capture preparation zone geometries in all tectonic settings, especially for submarine earthquakes where crustal structure and wave propagation differ substantially from continental regions.
Methodological constraints also merit acknowledgment. The K-means clustering required for association rule mining inevitably discretizes continuous MBT variations, potentially obscuring subtle nonlinear relationships. The presence of AMSR-2 fill values (655.35 K) in some clusters, while not affecting core findings about optimal channels, highlights the need for sophisticated quality control in operational systems. Additionally, potential confounding from extreme weather events, volcanic activity, or large-scale anthropogenic modifications remains incompletely characterized. While our model demonstrates improved discrimination capabilities, genuinely ambiguous cases---where non-seismic processes perfectly mimic precursor patterns---may still lead to false positives. This limitation underscores the importance of multi-parameter validation, as seismic preparation likely produces coherent anomalies across multiple geophysical observables \cite{hayakawaIntegratedAnalysisMultiParameter2024}.

\subsection{Advancing Earthquake Monitoring Through Physics-Informed AI}
This work establishes a transformative approach to satellite-based earthquake monitoring by demonstrating that knowledge-guided architectural design fundamentally outperforms generic deep learning. The remarkable alignment between mined frequency importance and architectural component sensitivity---with channels 0, 3, 4, and 9 showing threefold higher vulnerability to weight projection removal---confirms that our model has learned physically meaningful representations rather than statistical artifacts \cite{maoAdditionalMicrowaveRadiation2020}. This correspondence suggests that the hierarchical importance of model components mirrors the actual physical hierarchy of signal generation, from lithospheric stress accumulation through crustal deformation to surface electromagnetic emissions.
The implications extend beyond earthquake monitoring to broader challenges in Earth observation. By showing that pre-computed weights based on physical associations cannot be effectively replaced by learned parameters, we establish a new paradigm for geophysical AI: rather than treating neural networks as black boxes, we can encode domain knowledge directly into architectural innovations. This approach creates interpretable systems where model behavior aligns with physical understanding, enabling both improved predictions and deeper scientific insights.

Future research should prioritize several critical directions. First, validation across independent datasets from diverse tectonic settings will establish the generalizability of our frequency-polarization patterns. Second, integration with complementary observations---including GNSS-derived crustal strain, atmospheric gas concentrations, and ionospheric total electron content---will enable multi-parameter confirmation of detected anomalies. Third, development of region-specific models for moderate-magnitude earthquakes could expand operational relevance, particularly in areas where local amplification effects enhance precursor signals. Fourth, real-time implementation leveraging our identified optimal channels could enable continuous monitoring systems with adaptive thresholds based on local environmental conditions.
Most fundamentally, this convergence of geophysics and artificial intelligence opens new frontiers for understanding earthquake preparation processes. By creating AI systems that respect and incorporate physical constraints, we move beyond purely statistical approaches toward mechanistic understanding. This physics-informed machine learning paradigm promises not only enhanced natural hazard assessment but also new insights into the complex processes governing our dynamic planet. As we face increasing seismic risks in populated areas worldwide, such advances in early detection capabilities---grounded in both cutting-edge AI and fundamental geophysical understanding---become ever more critical for protecting lives and infrastructure.

In conclusion, by demonstrating that the signal ambiguity problem stems from methodological limitations rather than fundamental physical constraints, this work marks a turning point in earthquake science. The shift from universal to environment-specific detection methods not only achieves unprecedented accuracy but fundamentally alters our understanding of how Earth's surface responds to deep crustal stress. As we face increasing seismic risks in densely populated areas worldwide, this breakthrough in resolving the signal ambiguity problem—grounded in both cutting-edge AI and fundamental geophysical understanding—provides a foundation for the next generation of disaster preparedness systems.
\subsection{Toward Operational Implementation}

The resolution of the signal ambiguity problem opens immediate pathways toward operational deployment. A satellite-based monitoring system implementing our environment-specific approach could provide 2-20 day advance warning for major earthquakes, potentially saving thousands of lives annually \cite{akhoondzadehMultiPrecursorsAnalysis2018,qiSpatiotemporallyWeightedTwostep2020,qiMicrowaveBrightnessTemperature2022}. With over 3 billion people living in seismically active regions and annual earthquake damages exceeding \$40 billion globally \cite{dilleyNaturalDisasterHotspots2005,daniellLossesAssociatedSecondary2017,undrrGlobalAssessmentReport2019}, even modest improvements in short-term forecasting could yield enormous societal benefits.

Implementation would require: (1) real-time classification of global surface environments using existing satellite data, (2) continuous monitoring using the optimal frequency-polarization combinations identified for each zone, and (3) integration with ground-based monitoring systems for validation. Our framework provides the critical missing piece—a method to reliably distinguish genuine precursors from environmental noise—that has prevented such systems from being developed despite decades of effort.
%%% <<< FIX START: Add FloatBarrier to keep floats within this section >>> %%%
 \FloatBarrier 
%%% <<< FIX END >>> %%%
% FIX BOOKMARK: Changed \section* to \section
% FIX 1: No numbers will be displayed due to class file modification
\section{Methods} % Renamed from Methodology
This research employs a comprehensive methodological framework for detecting seismic precursors using microwave brightness temperature (MBT) data, as illustrated in Figure~\ref{fig:fig1}. The framework is specifically designed to address the signal ambiguity problem that has hindered operational earthquake monitoring. The framework integrates five key components: (1) surface type classification that categorizes the study area into distinct zones based on environmental characteristics; (2) structured data sampling that identifies earthquake-related and non-seismic MBT data using temporal and spatial criteria; (3) clustering analysis that transforms continuous MBT values into discrete clusters; (4) association rule mining that discovers frequency-polarization combinations with high predictive power for seismic events; and (5) a novel Weight-Enhanced Feature-Tailored Transformer (WE-FTT) model that synthesizes these components by pre-computing importance weights from support values and integrating them through parallel projection pathways and element-wise multiplication with feature embeddings. This integrated approach enables environment-specific anomaly detection by dynamically adjusting the importance of different frequency channels based on their relevance to seismic activity in each zone. The following sections detail each component of this methodological framework.

\subsection{Data Acquisition}
The microwave brightness temperature (MBT) data used in this study are derived from the Advanced Microwave Scanning Radiometer 2 (AMSR-2) instrument, which continues the legacy of AMSR on ADEOS-II and AMSR-E on Aqua. Since its launch in 2012, AMSR-2 has reliably measured global microwave emissions\cite{kasaharaStatusAMSR22012}. Operating at six frequency bands ranging from 7GHz to 89GHz, each with vertical and horizontal polarization, AMSR-2 provides 12 channels (Ten channels, specifically 6.9, 10.65, 23.8, 36.5, and 89.0 GHz for both H and V polarizations, were utilized in the final modeling stage). % Note: Removed reference to Section 3.5 as section numbers are not used
The spatial resolution (IFOV) varies by frequency, from about 24 $\times$ 42 km to 3 $\times$ 5 km\cite{kachiStatusGCOMW1AMSR22008}. This study focuses on nighttime (descending-mode) AMSR-2 MBT data to minimize interference from diurnal solar radiation and anthropogenic activity, thus providing a more stable baseline for anomaly detection \cite{maedaDiscriminationLocalFaint2008}. We used a 0.25$^{\circ}$ grid chosen for consistency with available auxiliary datasets (soil moisture, vegetation) and common practice in global studies \cite{njokuRetrievalLandSurface1999,wuIdentifyingSeismicAnomalies2024} cover January 2013 - August 2023, providing a long-term dataset for analyzing variability in microwave emissivity.
To more effectively identify microwave brightness temperature (MBT) anomalies potentially associated with seismic events, this study integrates multiple data sources and auxiliary information. First, we incorporate daily soil moisture data from NASA GES DISC (0.25$^{\circ}$ resolution)\cite{fangGlobalLandData2009} and vegetation coverage data from ERA5 (0.25$^{\circ}$ resolution, originally hourly but averaged to daily) \cite{hersbachERA5GlobalReanalysis2020,munoz-sabaterERA5landStateoftheartGlobal2021}. These datasets help characterize the surface environment and distinguish intrinsic land surface-driven MBT variations from those that might be related to earthquakes. Additionally, to establish a robust foundation for correlating MBT anomalies with seismic activity, we include a comprehensive global earthquake catalog sourced from the U.S. Geological Survey (USGS) website (\url{https://www.usgs.gov/programs/earthquake-hazards/earthquakes}). This dataset encompasses all earthquake events with magnitudes of 4.8 and above, totaling 32,123 occurrences from January 1, 2013, to August 1, 2023. Although the catalog includes events down to M$\ge$4.8, preliminary analyses indicated a lack of consistent MBT anomalies associated with smaller magnitudes in our global dataset. Therefore, to focus on potentially clearer signals and facilitate robust methodological development, this study restricts its primary analysis to major earthquakes (M$\ge$7.0), acknowledging that this limits direct applicability to smaller, more frequent events. Each event record provides essential information such as timing, location (latitude and longitude), and magnitude. By combining MBT observations with environmental conditions (soil moisture and vegetation) and a detailed earthquake catalog, this study creates a multidimensional analytical framework aimed at isolating subtle MBT anomalies and evaluating their potential linkage to seismic processes \cite{wuIdentifyingSeismicAnomalies2024,liuPreearthquakeMBTAnomalies2023}.

\subsection{Surface Type Classification}
Given the high sensitivity of MBT signals to surface environmental conditions (NbNS factors), a crucial first step towards isolating potential seismic signals is to analyze data within more homogeneous environmental contexts. Therefore, we classified the study area into five distinct zones based on vegetation coverage, soil moisture, and surface characteristics. The classification criteria are summarized in Table~\ref{tab:table2}. For implementation, we processed daily vegetation coverage data from ERA5 and soil moisture data from NASA GES DISC to match our 0.25$^{\circ}$ analysis grid. After applying the land-sea mask to identify Zone A, we classified terrestrial regions into Zones B-E using the threshold criteria. The earthquake catalog was then partitioned according to these zones based on event epicenter locations for subsequent environment-specific analysis. This classification enables us to account for varying environmental conditions affecting MBT measurements and develop targeted analytical approaches for each zone's unique characteristics \cite{oweMethodologySurfaceSoil2001}.

% --- Table 2: 最终完美版 ---
% 使用 makecell 和 tabularx 的组合方案，代码简洁且效果最佳
%%% <<< FIX START: Use [!htbp] for better placement >>> %%%
\begin{table}[!htbp]
%%% <<< FIX END >>> %%%
\centering
\caption{\label{tab:table2}Classification criteria for different surface types}
% 列定义非常简单：
% c: center-aligned，水平居中
% X: tabularx 的自动换行列，由于上面的 \renewcommand，它现在也是垂直居中的
\begin{tabularx}{\textwidth}{@{} c c c c X @{}} 
\toprule
% 为了完全匹配你的图片，我们可以使用 \makecell 来创建多行表头
\textbf{Zone} & \textbf{Surface Type} & \textbf{Vegetation Coverage} & \textbf{Soil Moisture} & \textbf{Characteristics} \\
\midrule
A & Marine Zone & N/A       & N/A       & Oceanic regions \\
%\addlinespace % booktabs 提供的命令，在行间增加一点漂亮的垂直间距
B & Humid Forest& $>0.5$    & $>6$      & Tropical rainforests, dense vegetation with moist soil \\
% \addlinespace
C & Dry Forest  & $>0.5$    & $\le 6$   & Subtropical savannas, significant vegetation with dry soil \\
% \addlinespace
D & Wetland     & $\le 0.5$ & $>6$      & Wetlands and marshes \\
%\addlinespace
E & Arid Land   & $\le 0.5$ & $\le 6$   & Deserts and arid areas \\
\bottomrule
\end{tabularx}
\end{table}

\subsection{Identification of Earthquake-Related MBT Data}
To identify MBT data associated with seismic events across different surface types, we established temporal and spatial search criteria for each zone (A-E), as shown in Figure~\ref{fig:fig11}. The figure illustrates the global distribution of major earthquakes (M$\ge$7.0) across different surface zones, from marine areas to various terrestrial environments. For temporal criteria, we examined MBT data within a window spanning from 20 days before to 10 days after each earthquake event (similar temporal windows are explored in, e.g., \cite{akhoondzadehMultiPrecursorsAnalysis2018,singhPrecursorySignalsUsing2010,qiSpatiotemporallyWeightedTwostep2020}, selected to encompass typical durations reported for potential short-term precursors and immediate post-seismic effects), aiming to capture both potential precursory signals and post-seismic effects. The spatial extent was determined using the Dobrovolsky radius \cite{dobrovolskyEstimationSizeEarthquake1979} (R = $10^{0.43M}$ km, where M is earthquake magnitude), a standard, albeit potentially simplified, empirical relationship commonly used to estimate the spatial extent of earthquake preparation zones, which defines the theoretical preparation zone for each earthquake. We focused on significant seismic events with magnitudes greater than 7.0, as these events are more likely to produce detectable thermal anomalies. For marine zones (Zone A), only shallow earthquakes with focal depths less than 70 km were considered, as deeper events are less likely to influence sea surface thermal emissions. MBT measurements were flagged if they fell within both the temporal and spatial windows of any qualifying earthquake event, with this process implemented through parallel computing to handle the large dataset efficiently. This identification process was performed independently for each surface classification to maintain the distinction between different environmental conditions.

% FIGURE 11
%%% <<< FIX START: Use [!htbp] for better placement >>> %%%
\begin{figure}[!htbp]
%%% <<< FIX END >>> %%%
\centering
%% IMAGE FIX: Assuming fig:fig11 corresponds to image11.png
\includegraphics[width=0.85\linewidth]{image11.png}
\caption{Global Distribution of Major Earthquakes (M$\ge$7.0) Across Different Surface Type Zones}
\label{fig:fig11}
\end{figure}

\subsection{Selection of Non-seismic MBT Data}
To establish a balanced dataset for comparative analysis, we performed random sampling of non-seismic MBT data for each surface type using a systematic sampling approach. For each zone, we selected an equal number of non-seismic samples as the corresponding earthquake-related data identified in the previous step. To establish a balanced dataset for comparative analysis, we created a corresponding non-seismic dataset for each environmental zone. For each zone, we randomly sampled a number of non-seismic data points equal to the number of earthquake-related samples identified in the previous step. For instance, in Zone A (Marine Zone), which contained 49,669,678 earthquake-related MBT measurements, we randomly selected an equal number of non-seismic measurements from periods and locations outside the seismic influence criteria. This process was repeated for each surface type (Zones B-E), resulting in ten distinct datasets: five containing earthquake-related MBT data and five containing randomly selected non-seismic data, with each pair corresponding to one of the surface types. This balanced sampling approach helps minimize potential bias in subsequent statistical analyses while maintaining the environmental characteristics specific to each surface type.


\subsection{Clustering Analysis and Association Rule Mining}
To effectively analyze the MBT data patterns, we implemented a two-stage data mining approach combining clustering analysis and association rule mining. For clustering analysis, after comparing hierarchical clustering, DBSCAN, and K-means algorithms, we selected the K-means algorithm for its computational efficiency with large-scale datasets \cite{hanConceptsTechniques2006}. The optimal number of clusters was determined using the Elbow Method by calculating the Sum of Squared Errors (SSE) for different K values \cite{thorndikeWhoBelongsFamily1953}. The clustering quality was validated using the Silhouette Coefficient, with values closer to 1 indicating better clustering results \cite{rousseeuwSilhouettesGraphicalAid1987}.
To address the challenge of signal ambiguity, we sought to identify consistent, albeit potentially subtle, MBT patterns that reliably precede earthquakes within specific environmental contexts. Association rule mining provides a suitable framework for this task. For association rule mining, we employed the Apriori algorithm to discover frequent patterns in MBT data\cite{agrawalMiningAssociationRules1993a}. The algorithm identifies frequent itemsets by leveraging the principle that subsets of frequent itemsets must also be frequent \cite{hanMiningFrequentPatterns2000a}. We first transformed the continuous MBT values from 10 AMSR-2 channels (specifically, 6.9H, 6.9V, 10.65H, 10.65V, 23.8H, 23.8V, 36.5H, 36.5V, 89.0H, 89.0V) into discrete clusters using the K-means results to meet the Apriori algorithm's input requirements. The mining process was conducted separately for earthquake-related and non-seismic data within each surface type (Zones A-E). The support-difference criterion specifically targets the discovery of itemsets whose prevalence significantly increases before earthquakes, providing the knowledge base needed to guide the model through ambiguous signal presentations.
To quantify the significance of different frequency combinations, we used an internal support-difference approach as our selection criterion, rather than a conventional fixed support and confidence threshold, by calculating the support difference between earthquake-related and non-seismic datasets for each frequent itemset. The support differences were then normalized to a 0-1 scale using min-max normalization to eliminate dimensional differences between frequency combinations and facilitate comparative analysis \cite{hanConceptsTechniques2006}. We retained only the itemsets with positive support differences, representing the most earthquake-sensitive frequency combinations. Finally, we calculated the average support for each individual frequency channel across all frequent itemsets, providing insights into channel sensitivity across different surface types. These results formed the basis for assigning appropriate weights to different frequency channels in subsequent analyses.

\subsection{Feature-based Transformer with Dynamic Weight Optimization}
\subsubsection{Weight-Enhanced Feature Processing}
% Note: Removed reference to Section 3.4
Based on the mining results, we implemented a preprocessing approach to feature weighting for the 10 columns of MBT data. To comprehensively consider the contribution of different frequencies and polarizations in earthquake prediction, we augmented each MBT data column with a corresponding weight column, pre-computed by adding the support values from frequent itemset mining to their initial value of 1. These pre-computed weights are provided alongside features as model inputs, creating a dual-input architecture that explicitly preserves the domain knowledge derived from our data mining process. This explicit injection of channel importance, derived from identifying consistent pre-seismic patterns, guides the model to prioritize these reliable features. This reduces its susceptibility to potentially noisy or misleading signals in less informative channels, thereby improving its ability to handle signal ambiguity. This weight-enhanced feature processing approach is visually represented in the parallel processing pathways on the left side of Figure~\ref{fig:fig12}, where features and their corresponding weights are processed through independent projection networks before being combined through element-wise multiplication.

% FIGURE 12 - Moved here, close to its dedicated subsection
%%% <<< FIX START: Use [!htbp] for better placement >>> %%%
\begin{figure}[!htbp]
%%% <<< FIX END >>> %%%
\centering
%% IMAGE FIX: Assuming fig:fig12 corresponds to image12.png
\includegraphics[width=0.85\linewidth]{image12.png}
\caption{Architectural overview of the Weight-Enhanced Feature-Tailored Transformer (WE-FTT). The model processes MBT features and corresponding weight vectors through parallel projection networks, followed by element-wise multiplication to create weighted feature representations. These representations undergo positional encoding and normalization before entering a series of attention blocks with residual connections. Key components include independent feature and weight projection pathways, multi-head attention mechanisms that operate on pre-weighted inputs, and a specialized fusion block for final classification.}
\label{fig:fig12}
\end{figure}

\subsubsection{Adaptive FT-Transformer Architecture}
We selected the FT-Transformer \cite{gorishniyRevisitingDeepLearning2021} as the core architecture of our deep learning model, which effectively encodes both discrete and continuous features from structured data into vectors. This enables Transformer-based feature extraction similar to text data processing \cite{vaswaniAttentionAllYou2017}. This choice leverages the model's excellence in handling time series data and long-range dependencies, while fully utilizing its advantages in processing high-dimensional feature spaces.
To better handle tabular data, we implemented several critical adaptations to the original FT-Transformer architecture. A significant modification was our specialized approach to sequence representation. Instead of introducing a separate CLS token as in standard transformer architectures, we implemented a more streamlined approach where feature embeddings are combined with their corresponding projected weight embeddings through element-wise multiplication. The LayerNorm was strategically positioned after the positional encoding, which we found was optimal for preserving the structural characteristics of MBT data.
The forward propagation process in our adapted architecture follows a carefully designed sequence of operations. The model begins by processing the input data through parallel projection pathways, transforming the 10 MBT features and their corresponding 10 weight values into embedded representations of equal dimensionality. These projections are then combined through element-wise multiplication, which effectively scales each feature's representation according to its associated weight, creating a weighted feature space that prioritizes the most seismically relevant channels.
The combined representation is enhanced with positional encoding and normalized before being processed through multiple attention blocks, each implementing our enhanced multi-headed attention mechanism combined with residual connections. The network's stability is maintained through strategically placed layer normalization operations, which help manage the flow of gradients and prevent training instability.
In the final stages of processing, the model extracts the processed feature representation, which by this point has accumulated a comprehensive understanding of the relationships between different MBT channels and their potential correlations with seismic activity. This representation is then passed through a prediction head, which generates the final classification output. These architectural modifications work in concert to enhance the model's ability to understand and process the complex structural features present in tabular MBT data, ultimately improving its seismic prediction capabilities.
The complete architecture of our Weight-Enhanced FT-Transformer is illustrated in Figure~\ref{fig:fig12}. The diagram visualizes the parallel processing pathways for both MBT features and their corresponding weight vectors, highlighting how these pathways converge through element-wise multiplication before a sequence dimension is added via the unsqueeze operation. The figure details the critical components including positional encoding, layered attention blocks with residual connections, and the specialized fusion mechanism that enables effective integration of weight information throughout the network depth. This architecture provides a comprehensive framework for capturing the complex interactions between different microwave frequency channels while maintaining sensitivity to seismically relevant patterns.


\subsubsection{Enhanced Multi-Head Attention Mechanism}
In our FT-Transformer architecture, we implemented a significantly enhanced multi-head attention mechanism specifically designed for MBT data analysis. The mechanism begins with an initial transformation phase, where a linear projection layer (qkv\_proj) simultaneously processes the input tensors into three distinct representations: query (Q), key (K), and value (V). This unified transformation ensures computational efficiency while maintaining the rich relationships between these different aspects of the input data. The resulting representations are then systematically divided into multiple attention heads, allowing the model to capture different aspects of the relationships between MBT features at various frequency bands. As shown in Figure~\ref{fig:fig12}, our multi-head attention mechanism achieves effective integration of feature and weight information through parallel processing of query (Q), key (K), and value (V) linear projections.
The attention score calculation process operates independently within each attention head, implementing a sophisticated mathematical framework. The core computation involves a matrix multiplication between the query matrix Q and the transposed key matrix K, which is then scaled by the square root of the head dimension to prevent gradient instability in deeper layers. This scaled attention mechanism is further refined through dropout regularization. Importantly, the weights' influence is already integrated into the input representation through the earlier element-wise multiplication of feature and weight embeddings, rather than directly modulating the attention scores themselves. This indirect approach to weight integration allows for more stable gradient flow while maintaining the relative importance of features. By operating on these pre-weighted feature embeddings, the multi-head attention mechanism can more effectively learn and focus on the complex inter-channel relationships identified as crucial for distinguishing seismic precursors, even when the overall signal pattern is atypical or obscured by noise.
Following the score calculation, the mechanism enters a critical processing phase where the raw attention scores are transformed into meaningful feature representations. The process begins by applying a softmax function to convert the attention scores into probability distributions, ensuring that the model's focus is appropriately normalized across all features. A dropout layer is then employed for regularization, helping to prevent overfitting and improve the model's generalization capabilities. These processed attention weights are subsequently multiplied with the value (V) representations to produce the output for each attention head, effectively capturing the weighted importance of different feature combinations.
The final stage of our attention mechanism focuses on integrating the diverse perspectives captured by each attention head. The outputs from all heads are concatenated into a unified representation, preserving the distinct patterns and relationships identified at different levels of abstraction. This consolidated representation then passes through an output projection layer (o\_proj), which transforms the concatenated attention outputs into the final feature representation. This carefully designed multi-stage process ensures that our attention mechanism can effectively capture both local and global patterns in the MBT data while maintaining sensitivity to seismically relevant feature interactions.

\subsection{Model Evaluation and Optimization}
To ensure optimal model performance, we implemented a comprehensive evaluation and parameter optimization framework. For addressing the class imbalance issue in seismic data prediction, we developed a Dynamic Focal Loss function that combines focal loss with adaptive class weighting. This loss function utilizes a gamma parameter (0.5-5.0) to adjust the focus on hard-to-classify samples and implements a momentum-based weight update mechanism (momentum: 0.5-0.99) to adaptively adjust class weights based on the model's recent performance history.
The model's effectiveness was evaluated using multiple metrics, with Matthews Correlation Coefficient (MCC) \cite{matthewsComparisonPredictedObserved1975} serving as the primary optimization metric due to its robustness in handling imbalanced multi-class problems. Additional metrics including accuracy, weighted precision-recall, F1 score, and Cohen's Kappa \cite{cohenCoefficientAgreementNominal1960} were also monitored to provide a comprehensive assessment of model performance across different surface type zones.
For hyperparameter optimization, we employed Optuna \cite{akibaOptunaNextgenerationHyperparameter2019} to systematically explore the parameter space through 30 trials with early stopping. Key parameters optimized included architectural components (number of attention heads: 4-32, input embedding dimension: 64-512, attention blocks: 2-8), and training parameters (learning rate: 1e-5 to 1e-2, weight decay: 1e-6 to 1e-3). The training process utilized a distributed setup across multiple GPUs, with an 80/20 train-test split and gradient clipping (0.5-5.0) to ensure stability. This systematic optimization approach, combined with our enhanced model architecture and weight optimization strategy, resulted in robust predictive performance across different surface types and seismic conditions.
Additionally, we conducted comprehensive ablation studies to evaluate the contribution of individual architectural components, with particular focus on weight projection, attention mechanisms, and feature fusion strategies.

\subsection{Model Comparison}
To evaluate the effectiveness of the proposed frequency itemset-based weight optimization methodology, we conducted comprehensive comparative experiments utilizing three distinct architectural approaches: our Weight-Enhanced FT-Transformer (WE-FTT), TabNet\cite{arikTabNetAttentiveInterpretable2021}, and CatBoost \cite{prokhorenkovaCatBoostUnbiasedBoosting2018}. The experimental framework maintained consistent input features across all models, comprising 10 MBT measurements (BT\_06\_H/V through BT\_89\_H/V) and their corresponding weight vectors derived from support values.

The integration of support-based weights manifests differently across the three architectural paradigms, each employing distinct mechanisms for incorporating weight information into their respective learning processes. In the Weight-Enhanced FT-Transformer architecture, weights influence the network through a parallel projection pathway. This approach implements a sophisticated feature-weight integration scheme where channel-specific weights derived from support values are projected into the same embedding space as features, followed by element-wise multiplication prior to attention computation. This pre-weighting of features indirectly influences the attention mechanism by adjusting the relative importance of different channels in the representation space. The architecture further enhances this weight integration through residual connections and layer normalization, maintaining the influence of support-based weights throughout the network's depth.

The TabNet architecture approaches weight integration through a fundamentally different mechanism, employing a sequential feature selection strategy. This model implements a dual-pathway approach to weight utilization: first, through direct feature-level weighting in the feature selection masks, and second, through sample-level importance weighting in the loss function computation. The feature selection process is guided by learnable masks that are influenced by the support-based weights, allowing the model to adaptively focus on the most relevant features at each decision step. This sequential processing enables the model to capture complex feature interactions while maintaining interpretability through explicit feature selection.

In contrast, the CatBoost architecture integrates weights through its gradient boosting framework, incorporating them directly into the tree construction process. The model utilizes weights in multiple aspects of its learning procedure: in the selection of split points during tree construction, in the calculation of leaf values, and in the bootstrap sampling process for building individual trees. This comprehensive weight integration strategy affects both the model's structure and its learning dynamics, with weights influencing both the feature selection process and the final prediction computation.

Each architectural approach presents distinct advantages in handling weighted features. The WE-FTT's architecture excels in capturing complex feature interactions through its unique parallel projection and element-wise multiplication approach, which maintains the relative importance of features throughout the network by establishing weighted representations before attention computation. TabNet's sequential feature selection provides explicit interpretability in feature utilization, particularly valuable in analyzing seismic patterns. CatBoost's tree-based approach offers robust handling of weighted features through its gradient boosting framework, particularly effective in capturing non-linear relationships in the data. Compared to other architectural approaches, our WE-FTT model, as illustrated in Figure~\ref{fig:fig12}, achieves superior performance in seismic precursor detection through its distinctive feature-weight parallel processing pathways and multi-layered attention mechanisms, enabling more refined weight modulation when analyzing microwave brightness temperature data.

To further validate our approach, we extended our comparative analysis to include several classical machine learning models: XGBoost, LightGBM, Multi-Layer Perceptron (MLP), and Random Forest. These models were selected for their distinct approaches to feature weight integration. The gradient boosting frameworks (XGBoost and LightGBM) incorporate weight information through instance-based weighted gradient calculations, with XGBoost directly scaling the gradients during tree construction and LightGBM employing a histogram-based approach for weight integration. The MLP serves as a deep learning baseline, implementing weight integration through feature concatenation and fully connected layers, while the Random Forest provides an ensemble perspective through weighted bootstrap sampling and voting mechanisms. While these classical models demonstrate robust performance in tabular data analysis, their straightforward weight integration mechanisms contrast with our Weight-Enhanced FT-Transformer's sophisticated attention-based approach. The gradient boosting models excel in capturing non-linear relationships but lack dynamic feature interaction capabilities, while both MLP and Random Forest show limitations in adapting to dynamic weight adjustments during prediction.

%%% <<< FIX START: Add FloatBarrier to keep floats within this section >>> %%%
% Force all floats defined in Methods section to be placed before Acknowledgements.
\FloatBarrier 
%%% <<< FIX END >>> %%%

% Stop line numbering before Acknowledgements
\nolinenumbers

% FIX BOOKMARK: Keep \section*, but add anchor and TOC line manually
\section*{Acknowledgements}
\phantomsection % Create an anchor for hyperref
\addcontentsline{toc}{section}{Acknowledgements} % Add bookmark linking to anchor
We thank the data providers for making their datasets publicly available. This work was supported by the Natural Science Foundation of China (NSFC) under grant number 42274108 and the Central Public-interest Scientific Institution Basal Research Fund (No. CEAIEF20240405). 

%% !! AUTHOR CONTRIBUTION IS MANDATORY - PLEASE EDIT THE PLACEHOLDER !! 
% FIX BOOKMARK: Keep \section*, but add anchor and TOC line manually
\section*{Author contributions statement}
\phantomsection % Create an anchor for hyperref
\addcontentsline{toc}{section}{Author contributions statement} % Add bookmark
%Must include all authors, identified by initials.
% Placeholder based on author order and roles - PLEASE VERIFY AND CORRECT
P.X., C.L., and X.S. conceived the study. P.X. and C.L. performed the analysis and modelling. H.Z., R.B., A.D.S and X.S. supervised the work, contributed to interpretation and discussion \cite{jeongDeepLearningBased2024}. P.X. wrote the initial manuscript. All authors discussed the results and reviewed the manuscript. 

% FIX BOOKMARK: Keep \section*, but add anchor and TOC line manually
\section*{Additional information}
\phantomsection % Create an anchor for hyperref
\addcontentsline{toc}{section}{Additional information} % Add bookmark
\textbf{Data Availability} 
All data supporting the findings of this study are publicly available. The AMSR-2 brightness temperature datasets (2013--2023) analyzed in this study can be accessed through the JAXA GCOM-W1 data archive (\url{https://gcom-w1.jaxa.jp/}). Daily ERA5-Land reanalysis data for vegetation cover are available from the ECMWF data portal (\url{https://www.ecmwf.int/}), and GLDAS soil moisture data were obtained from NASA's Hydrology Data and Information Services Center (HDISC) (\url{https://disc.gsfc.nasa.gov/}). The global earthquake catalog was downloaded from the USGS Earthquake Hazards Program (\url{https://www.usgs.gov/programs/earthquake-hazards/earthquakes}). All processed datasets generated during the current study (such as discretized time series and derived association rules) are available from the corresponding author upon reasonable request.

\textbf{Code Availability} 
The PyTorch implementation of the Weight-Enhanced Feature-Tailored Transformer (WE-FTT), along with all code for data preprocessing, model training, and analysis presented in this study, is publicly available on GitHub at \url{https://github.com/PANXIONG-CN/WE-FTT} under the MIT License.

\textbf{Competing interests} The authors declare no competing interests.

% Bibliography
% ERROR 2 FIX: Added bibliography style. 
% Change 'unsrt' to the style required by the journal (e.g., naturemag, plain, or wlscirep if a .bst file exists).
% The wlscirep class might define its own style, but explicitly stating it is safer and a common omission.
% The class file *does* define \bibliographystyle{naturemag-doi}, so we only need the \bibliography command.
% \bibliographystyle{unsrt} 
% FIX BOOKMARK: Add anchor and TOC line manually BEFORE the bibliography command
% The class file modification removes the conflicting addcontentsline
\phantomsection % Create an anchor for hyperref
\addcontentsline{toc}{section}{References} % Add bookmark linking to the anchor JUST created
\bibliography{references} % references the .bib file - make sure references.bib exists!


\end{document}
% --- END OF FILE main-tex.tex ---