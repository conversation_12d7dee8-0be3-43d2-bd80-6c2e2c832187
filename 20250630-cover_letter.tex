\documentclass[11pt, a4paper]{letter} 
\usepackage{geometry}
\usepackage{amsmath}
\usepackage{enumitem}
\usepackage[colorlinks=true, urlcolor=blue, bookmarks=true]{hyperref}
\usepackage{bookmark}

\geometry{left=1.5cm, right=1.5cm, top=1.2cm, bottom=1.2cm} 

\address{} 

\signature{
<PERSON><PERSON>, Ph.D. \\
Professor, National Space Science Center, Chinese Academy of Sciences \\
\href{mailto:<EMAIL>}{<EMAIL>}
}
 
\date{\today} 

\begin{document}

\begin{letter}{
The Editors\\
\emph{Nature Communications}
}

\opening{Dear Editors,}

Reliable satellite-based earthquake monitoring has remained an elusive goal for over five decades, despite its immense potential for disaster mitigation. The central obstacle—the "signal ambiguity problem"—has prevented operational implementation of satellite monitoring systems. We are pleased to submit our manuscript, ``Resolving the signal ambiguity problem in satellite earthquake monitoring through environment-specific AI,'' which presents a robust pathway to overcome this long-standing barrier.

The signal ambiguity problem manifests as a frustrating paradox: identical microwave anomalies can precede either catastrophic earthquakes or nothing at all, while many major earthquakes strike without detectable precursors. This ambiguity has rendered satellite monitoring largely ineffective for operational use. Our analysis reveals that this problem stems not from inherent physical limitations but from a methodological blind spot—the assumption that universal detection methods can work across Earth's heterogeneous surface.

Our manuscript demonstrates a paradigm shift through a knowledge-guided deep learning framework that effectively resolves this ambiguity:

\begin{enumerate}[label=\arabic*., font=\bfseries]
    \item We \textbf{abandon the "one-size-fits-all" approach} by classifying Earth's surface into five distinct environmental zones, recognizing that reliable precursor signatures are environment-specific rather than universal.
    \item We \textbf{discover zone-specific frequency-polarization signatures} that consistently indicate pre-seismic conditions, using association rule mining on 346.56 million microwave measurements from 154 major earthquakes (M$\ge$7.0) over a decade.
    \item We \textbf{implement this knowledge} through our Weight-Enhanced Feature-Tailored Transformer (WE-FTT), which integrates these environment-specific patterns as prior knowledge, enabling the model to distinguish genuine precursors from environmental noise.
\end{enumerate}

Our results demonstrate that accounting for environmental heterogeneity transforms previously ambiguous signals into clear indicators. Each environment exhibits distinct, highly reliable precursor signatures—marine zones through 89 GHz H-polarization combinations, arid regions via 23.8/36.5 GHz patterns—achieving unprecedented discrimination capability (MCC $\sim$0.84, substantially outperforming conventional approaches at 0.74). Comprehensive ablation studies confirm that this environment-specific knowledge integration is crucial for resolving the ambiguity.

This work represents the type of transformative research that \emph{Nature Communications} champions. By demonstrating a robust solution to a problem that has hindered earthquake science for decades, we show how integrating domain knowledge into AI architectures can address fundamental challenges in Earth observation. With over 3 billion people living in seismically active regions and annual earthquake damages exceeding \$40 billion, even modest improvements in short-term earthquake forecasting could yield significant societal benefits. Moreover, our approach of resolving signal ambiguity through environment-specific analysis offers a new paradigm applicable to other Earth monitoring challenges where heterogeneous surface conditions confound signal interpretation.

This original work is not published or under consideration elsewhere. All authors approve submission and declare no competing interests. We believe this manuscript will be of broad interest to your readership, spanning earthquake science, satellite remote sensing, and AI applications in geophysics.

Thank you for your consideration.

\closing{Sincerely,}

\end{letter}
\end{document}